\documentclass[12pt,a4paper]{article}
\usepackage[utf8]{inputenc}
\usepackage[french]{babel}
\usepackage[T1]{fontenc}
\usepackage{graphicx}
\usepackage{longtable}
\usepackage{booktabs}
\usepackage{hyperref}
\usepackage{geometry}
\geometry{margin=2.5cm}

\title{Projet de Fin d'Année - Showroom E-commerce avec Intelligence Artificielle}
\author{Votre Nom}
\date{\today}

\begin{document}

\maketitle
\tableofcontents
\newpage

\section{Introduction Générale}

Dans un monde où la digitalisation est devenue un facteur clé de succès,
les entreprises doivent s'adapter en permanence aux nouvelles
technologies pour offrir une expérience optimale à leurs clients et
améliorer leur performance. Le secteur du commerce, notamment celui de
la décoration et du linge de maison, n'échappe pas à cette
transformation. Les consommateurs recherchent aujourd'hui des
expériences d'achat fluides, personnalisées et interactives, que ce soit
en ligne ou en magasin.

Dans ce contexte, notre projet propose une solution innovante et
complète pour un showroom spécialisé dans la décoration et le linge de
maison, combinant les avantages du commerce en ligne et de
l'intelligence artificielle. Ce projet repose sur deux axes
fondamentaux, chacun apportant une valeur ajoutée significative à
l'expérience client et à la gestion du showroom.

Tout d'abord, le site web e-commerce interactif constitue la pierre
angulaire de notre solution. Il permet aux clients d'explorer une large
gamme de produits, de passer des commandes en ligne et de bénéficier
d'un service de recommandation basé sur des produits similaires. Du côté
des administrateurs, l'interface de gestion facilite la mise à jour du
catalogue, le suivi des stocks et le traitement des commandes,
garantissant ainsi une gestion efficace du showroom.

Enfin, l'intégration de l'intelligence artificielle au projet apporte
une dimension innovante à l'expérience en magasin. Un module d'analyse
émotionnelle basé sur la reconnaissance faciale est mis en place pour
détecter les émotions des clients à leur entrée et à leur sortie du
showroom. Cette analyse permet d'évaluer leur satisfaction en temps réel
et d'adapter les services en conséquence. En identifiant les points de
friction et en améliorant l'accueil et l'interaction avec les clients,
le showroom peut continuellement perfectionner son offre et renforcer la
fidélité de sa clientèle.

Ce document présente en détail les étapes de conception et de
développement de notre projet. Nous y explorerons les différents choix
technologiques effectués, la méthodologie adoptée, ainsi que les
solutions mises en place pour répondre aux besoins des utilisateurs.
L'objectif est de démontrer comment la fusion entre e-commerce et
Intelligence Artificielle permet d'offrir une solution innovante et
performante pour un showroom moderne, en parfaite adéquation avec les
attentes des consommateurs d'aujourd'hui.

\textbf{Chapitre 1~: Cadre Général du Projet}

\begin{enumerate}
\def\labelenumi{\arabic{enumi}.}
\item
  \textbf{Introduction}
\end{enumerate}

Dans un contexte où la digitalisation devient un levier incontournable
de développement, les entreprises spécialisées dans la décoration et le
linge de maison doivent innover pour répondre aux attentes croissantes
des consommateurs. L'intégration des nouvelles technologies permet non
seulement d'optimiser la gestion des ventes, mais aussi d'améliorer
l'expérience client à travers des solutions interactives et
intelligentes.

C'est dans cette optique que notre projet a vu le jour, avec pour
ambition de digitaliser la gestion d'un showroom regroupant quatre
marques internationales. Cette digitalisation repose sur la mise en
place d'une plateforme complète intégrant un site e-commerce et un
module d'Intelligence Artificielle (IA) destiné à mesurer la
satisfaction des clients via une analyse émotionnelle.

Ce projet vise donc à offrir aux clients une expérience d'achat fluide
et personnalisée tout en apportant aux administrateurs des outils
avancés pour améliorer la gestion.

\begin{enumerate}
\def\labelenumi{\arabic{enumi}.}
\setcounter{enumi}{1}
\item
  \textbf{Cadre du Projet}
\end{enumerate}

Le projet s'inscrit dans une démarche d'innovation technologique visant
à transformer un showroom traditionnel en une plateforme intelligente et
performante. L'objectif principal est d'optimiser la gestion des
produits et des commandes tout en exploitant les données clients afin de
personnaliser l'expérience utilisateur.

En combinant les technologies web modernes et l'IA, notre solution
ambitionne de répondre aux défis suivants :

\begin{itemize}
\item
  Automatiser et fluidifier la gestion des commandes et des stocks.
\item
  Évaluer la satisfaction des clients grâce à la reconnaissance faciale
  dans le showroom physique.
\end{itemize}

\begin{enumerate}
\def\labelenumi{\arabic{enumi}.}
\setcounter{enumi}{2}
\item
  \textbf{Présentation du Showroom}
\end{enumerate}

Le showroom concerné est un espace dédié à la décoration et au linge de
maison, représentant quatre marques internationales reconnues. Il
propose une large gamme de produits allant du mobilier aux accessoires
de décoration, en passant par le linge de maison de haute qualité.

Ce showroom cherche à améliorer son efficacité en adoptant des outils
numériques qui permettent d'interagir avec les clients de manière plus
dynamique. L'idée est de fusionner l'expérience physique et digitale
pour offrir aux clients une approche omnicanale, où ils peuvent
consulter les produits en ligne, obtenir des recommandations et
finaliser leurs achats en magasin ou via la plateforme e-commerce.

\begin{enumerate}
\def\labelenumi{\arabic{enumi}.}
\setcounter{enumi}{3}
\item
  \textbf{Présentation du Projet}
\end{enumerate}

Notre projet repose sur une plateforme web intégrée, permettant
d'unifier la gestion des commandes, d'exploiter les données clients et
de mettre en place une intelligence artificielle pour l'analyse
émotionnelle.

\begin{itemize}
\item
  Cette solution est articulée autour de deux grandes fonctionnalités
  :\\
  Un site e-commerce interactif qui permet aux clients d'explorer les
  produits, d'effectuer des achats et de bénéficier d'un moteur de
  recommandations personnalisé.
\item
  Un système d'Intelligence Artificielle (IA) qui détecte les émotions
  des clients à leur entrée et sortie du showroom pour évaluer leur
  satisfaction.
\end{itemize}

Grâce à cette solution, les clients bénéficient d'une expérience plus
fluide et intuitive, tandis que les administrateurs disposent d'outils
avancés pour optimiser la gestion et la rentabilité du showroom.

\begin{enumerate}
\def\labelenumi{\arabic{enumi}.}
\setcounter{enumi}{4}
\item
  \textbf{Problématique}
\end{enumerate}

Avec l'évolution rapide du commerce digital et l'essor de l'intelligence
artificielle, il devient impératif pour les entreprises de s'adapter aux
nouvelles attentes des consommateurs tout en optimisant leurs
opérations.

Ainsi, notre projet répond à la problématique suivante :\\
Comment digitaliser efficacement un showroom spécialisé dans la
décoration et le linge de maison en intégrant des technologies web et IA
afin d'améliorer la gestion des commandes, l'expérience client et
l'adaptation des services ?

Ce questionnement soulève plusieurs défis techniques et fonctionnels,
notamment :

\begin{itemize}
\item
  La mise en place d'une plateforme intuitive et performante.
\item
  L'analyse émotionnelle des visiteurs pour mesurer leur satisfaction et
  ajuster les services
\end{itemize}

\begin{enumerate}
\def\labelenumi{\arabic{enumi}.}
\setcounter{enumi}{5}
\item
  \textbf{Expression des Besoins}
\end{enumerate}

Les besoins identifiés dans ce projet concernent trois catégories
d'utilisateurs :

\begin{enumerate}
\def\labelenumi{\arabic{enumi}.}
\item
  \textbf{Besoin des Clients}
\end{enumerate}

\begin{itemize}
\item
  Accéder à un site ergonomique et fluide pour explorer les produits et
  passer des commandes.
\item
  Recevoir des recommandations personnalisées avec une sorte de chatbot
  statique pour répondre à ces questions.
\item
  Pouvoir choisir entre une livraison à domicile ou un retrait en
  showroom.

  \begin{enumerate}
  \def\labelenumi{\arabic{enumi}.}
  \item
    \textbf{Besoin des Administrateurs}
  \end{enumerate}
\end{itemize}

\begin{itemize}
\item
  Disposer d'une interface de gestion des commandes, des stocks et des
  clients.
\item
  Gérer le catalogue de produits et mettre à jour les offres en temps
  réel.

  \begin{enumerate}
  \def\labelenumi{\arabic{enumi}.}
  \item
    \textbf{Besoin des Décisionnaires}
  \end{enumerate}
\end{itemize}

\begin{itemize}
\item
  Exploiter l'IA pour évaluer le niveau de satisfaction des clients en
  showroom.
\item
  Optimiser l'expérience utilisateur en s'appuyant sur les données
  collectées.
\end{itemize}

\begin{enumerate}
\def\labelenumi{\arabic{enumi}.}
\setcounter{enumi}{6}
\item
  \textbf{Architecture de l'Application}
\end{enumerate}

Notre projet repose sur une architecture modulaire qui combine un site
e-commerce et une extension en intelligence artificielle (IA). Le
front-end est développé avec React.js pour offrir une interface
utilisateur dynamique et réactive. Le back-end est basé sur Laravel,
garantissant une gestion fluide des données et des API. La base de
données utilisée est PostgreSQL, choisie pour sa robustesse et sa
compatibilité avec les structures complexes.

Pour la gestion des utilisateurs et des accès, nous avons intégré
Keycloak, une solution open-source qui sécurise l'authentification et
les rôles. Enfin, une perspective IA a été ajoutée avec un module
d'analyse des émotions, basé sur la reconnaissance faciale, afin
d'évaluer la satisfaction des clients. Cette architecture assure
performance, évolutivité et ouverture à de futures améliorations.

\begin{enumerate}
\def\labelenumi{\arabic{enumi}.}
\setcounter{enumi}{7}
\item
  \textbf{Définition des Concepts de Base}
\end{enumerate}

Pour bien comprendre la portée du projet, il est essentiel de définir
les principaux concepts autour desquels il est construit :

\begin{enumerate}
\def\labelenumi{\arabic{enumi}.}
\item
  \textbf{Showroom}
\end{enumerate}

Il s'agit d'un espace physique de présentation de produits, généralement
destiné à exposer les nouveautés ou les produits phares d'une marque.
Dans notre contexte, le showroom permet aux clients de découvrir et
tester les articles de décoration et linge de maison avant de les
acheter, tout en servant de point de contact entre l'expérience physique
et digitale.

\begin{enumerate}
\def\labelenumi{\arabic{enumi}.}
\setcounter{enumi}{1}
\item
  \textbf{Site e-commerce}
\end{enumerate}

C'est une plateforme en ligne permettant aux clients de consulter un
catalogue de produits, de passer des commandes et de suivre leurs achats
à distance. Elle permet une extension numérique du showroom, accessible
24h/24.

\begin{enumerate}
\def\labelenumi{\arabic{enumi}.}
\setcounter{enumi}{2}
\item
  \textbf{Intelligence Artificielle (IA)}
\end{enumerate}

Discipline visant à simuler l'intelligence humaine à travers des
algorithmes. L'IA est ici utilisée pour analyser les expressions
faciales des clients dans le showroom, afin d'estimer leur niveau de
satisfaction.

\begin{enumerate}
\def\labelenumi{\arabic{enumi}.}
\setcounter{enumi}{8}
\item
  \textbf{Conclusion}
\end{enumerate}

Ce projet vise à transformer l'expérience client et la gestion d'un
showroom spécialisé dans la décoration et le linge de maison en
intégrant des technologies avancées. En combinant des outils web et
d'Intelligence Artificielle, il permet une gestion optimisée des
produits et des ventes, tout en offrant une approche innovante pour
comprendre et améliorer la satisfaction des clients.

Cette solution complète et intelligente répond aux exigences du commerce
moderne en proposant une expérience digitale enrichie, à la fois pour
les clients et les gestionnaires du showroom.

\textbf{Chapitre 2~: Pilotage du Projet et Conception Générale}

\begin{enumerate}
\def\labelenumi{\arabic{enumi}.}
\item
  \textbf{Introduction}
\end{enumerate}

Le succès d'un projet repose sur une gestion efficace et une conception
bien structurée. Cette section présente l'approche adoptée pour piloter
le projet, en détaillant les besoins fonctionnels et non fonctionnels
qui ont guidé le développement de la solution.\\
Dans le cadre de ce projet, une méthodologie agile a été privilégiée
afin d'assurer une adaptation rapide aux exigences des utilisateurs et
aux évolutions du marché. L'accent a été mis sur une planification
rigoureuse, une définition claire des objectifs et une répartition
efficace des tâches entre les membres de l'équipe.

L'analyse des besoins a constitué une étape fondamentale dans la
conception de la solution. Elle a permis d'identifier les
fonctionnalités essentielles du système, de définir les contraintes
techniques et de garantir que la plateforme réponde aux attentes des
clients et des administrateurs du showroom.

\begin{enumerate}
\def\labelenumi{\arabic{enumi}.}
\setcounter{enumi}{1}
\item
  \textbf{Analyse des Besoins}
\end{enumerate}

L'analyse des besoins vise à déterminer les exigences du projet afin
d'assurer la pertinence et l'efficacité de la solution développée. Cette
analyse se divise en deux catégories : les \textbf{besoins
fonctionnels}, qui décrivent les services que doit offrir l'application,
et les \textbf{besoins non fonctionnels}, qui définissent les
contraintes techniques et qualitatives du système.

\begin{enumerate}
\def\labelenumi{\arabic{enumi}.}
\item
  \textbf{Besoins Fonctionnels}
\end{enumerate}

Les besoins fonctionnels décrivent les fonctionnalités attendues du
système pour répondre aux attentes des utilisateurs finaux. Ils sont
structurés en plusieurs modules :

\begin{itemize}
\item
  \textbf{Gestion des produits} : Permet aux administrateurs d'ajouter,
  modifier, supprimer et catégoriser les produits disponibles dans le
  showroom.
\item
  \textbf{Système de recommandations} : Propose aux clients des produits
  basés sur leurs achats précédents et sur les tendances du marché.
\item
  \textbf{Gestion des paiements} : Intègre divers moyens de paiement
  sécurisés, notamment les paiements en ligne et à la livraison.
\item
  \textbf{Module d'intelligence artificielle pour l'analyse
  émotionnelle} : Analyse les expressions faciales des visiteurs du
  showroom pour évaluer leur satisfaction et améliorer leur expérience
  en magasin.
\item
  \textbf{Notifications et communication} : Envoie des notifications par
  email ou SMS aux clients pour les informer des promotions, des
  confirmations de commande et des recommandations personnalisées.
\item
  \textbf{Gestion des utilisateurs et des rôles} : Le système gère
  l'inscription des clients, leur profil, leurs commandes, ainsi que les
  droits d'accès des administrateurs.
\item
  \textbf{Navigation et recherche produit} : Système de filtre, tri et
  recherche avancée par nom, marque, catégorie, prix, etc.

  \begin{enumerate}
  \def\labelenumi{\arabic{enumi}.}
  \item
    \textbf{Besoins Non Fonctionnels}
  \end{enumerate}
\end{itemize}

Les besoins non fonctionnels concernent les contraintes et
caractéristiques du système qui garantissent une performance optimale et
une expérience utilisateur fluide. Ils englobent plusieurs aspects :

\begin{itemize}
\item
  \textbf{Sécurité et confidentialité} : Mise en place de protocoles de
  sécurité robustes pour protéger les données des utilisateurs et
  assurer des transactions sécurisées.
\item
  \textbf{Accessibilité et ergonomie} : Développement d'une interface
  intuitive et responsive pour offrir une navigation fluide sur
  différents appareils (PC, tablettes, smartphones).
\item
  \textbf{Performance et rapidité} : Optimisation du temps de réponse du
  système afin d'assurer un chargement rapide des pages et une exécution
  efficace des requêtes.
\item
  \textbf{Scalabilité} : Conception d'une architecture évolutive
  permettant d'ajouter de nouvelles fonctionnalités sans impacter les
  performances globales du système.
\item
  \textbf{Fiabilité} : Garantir un fonctionnement stable du site avec
  une tolérance aux pannes et un système de sauvegarde automatique des
  données.
\item
  \textbf{Disponibilité 24/7} : Assurer un accès continu à la plateforme
  à tout moment, y compris en dehors des horaires d'ouverture physique
  du showroom.
\end{itemize}

\begin{enumerate}
\def\labelenumi{\arabic{enumi}.}
\setcounter{enumi}{2}
\item
  \textbf{Conception de la solution}

  \begin{enumerate}
  \def\labelenumii{\arabic{enumii}.}
  \item
    \textbf{Architecture de l'application}
  \end{enumerate}
\end{enumerate}

L'architecture de l'application repose sur un modèle Client-Serveur,
garantissant une séparation claire des responsabilités entre le frontend
et le backend. Cela permet de faciliter l'évolution du système tout en
maintenant des performances optimales, une scalabilité à long terme et
une sécurité renforcée, notamment via l'intégration de Keycloak pour
l'authentification.\\
L'application adopte une architecture modulaire qui sépare clairement
les différentes couches de l'application pour assurer une gestion
optimisée des données, des utilisateurs, des interactions et des
services externes. Cette approche est adaptée à une solution e-commerce
spécialisée dans la décoration et le linge de maison, avec un volet
d'Intelligence Artificielle (IA) permettant de personnaliser les
recommandations de produits et d'analyser la satisfaction des clients
via des outils d'analyse émotionnelle.\\
L'architecture est structurée autour de plusieurs couches principales :

\begin{itemize}
\item
  \textbf{Frontend (Client)} : L'interface utilisateur, développée avec
  React.js, permet aux clients d'interagir avec l'application,
  d'explorer les produits, de passer des commandes, et de recevoir des
  recommandations basées sur leur comportement d'achat.
\item
  \textbf{Backend (Serveur)} : Le serveur backend, développé avec
  Laravel, expose des API RESTful pour traiter les données des
  utilisateurs, gérer les commandes, et interagir avec la base de
  données. Ce backend prend également en charge l'intégration des
  fonctionnalités liées à l'Intelligence Artificielle, comme l'analyse
  des émotions des clients ou la génération de recommandations
  personnalisées.
\item
  \textbf{Base de données} : La base de données relationnelle PostgreSQL
  est utilisée pour stocker toutes les informations relatives aux
  clients, produits, commandes et historiques d'achats. Elle est conçue
  pour garantir une gestion sécurisée et efficace des données, avec des
  performances élevées pour la gestion de grandes quantités
  d'informations.
\end{itemize}

\hypertarget{composants-principaux-de-larchitecture}{%
\paragraph{Composants principaux de
l'architecture}\label{composants-principaux-de-larchitecture}}

\begin{itemize}
\item
  \textbf{Frontend (React.js)} : Interface dynamique qui consomme les
  API du backend et permet aux utilisateurs d'effectuer des achats, de
  consulter les produits, et de bénéficier de recommandations
  personnalisées basées sur l'IA.
\item
  \textbf{Backend (Laravel)} : Le backend expose des points d'API
  RESTful qui permettent d'interagir avec les données stockées dans la
  base de données et d'assurer des fonctionnalités avancées comme la
  gestion des utilisateurs via Keycloak et l'intégration des services
  d'IA pour l'analyse des émotions et des recommandations.
\item
  \textbf{Base de données (PostgreSQL)} : Contient toutes les
  informations liées aux clients, aux produits, aux commandes et aux
  historiques d'achat. Elle est optimisée pour supporter des requêtes
  complexes tout en garantissant la sécurité et l'intégrité des données.
\item
  \textbf{Keycloak (Authentification)} : Keycloak est utilisé pour gérer
  l'authentification et l'autorisation des utilisateurs. Ce système
  d'identité centralisé permet de sécuriser l'accès aux ressources de
  l'application et d'assurer que seules les personnes autorisées
  puissent accéder aux données sensibles. Keycloak gère également les
  rôles et permissions des utilisateurs (client, administrateur, etc.),
  et peut intégrer des protocoles comme OAuth 2.0 et OpenID Connect pour
  faciliter la gestion des sessions.
\item
  \textbf{Services d'Intelligence Artificielle (IA)} : Des modèles d'IA,
  comme le traitement des émotions par reconnaissance faciale et la
  génération de recommandations personnalisées, sont intégrés dans
  l'architecture. Ces services d'IA permettent de personnaliser
  l'expérience utilisateur, notamment en analysant les émotions des
  clients dans le showroom physique et en recommandant des produits en
  fonction de leurs préférences et comportements d'achat.

  \begin{enumerate}
  \def\labelenumi{\arabic{enumi}.}
  \item
    \textbf{Choix technologiques}
  \end{enumerate}
\end{itemize}

Le choix des technologies repose sur des critères de performance, de
sécurité, de maintenabilité et de conformité aux exigences
fonctionnelles du projet. Les technologies sont adaptées pour gérer une
solution de e-commerce complexe avec une forte composante d'IA et de BI.

\begin{enumerate}
\def\labelenumi{\arabic{enumi}.}
\item
  \textbf{Frontend}
\end{enumerate}

\begin{itemize}
\item
  \textbf{React.js} : Ce Framework JavaScript est idéal pour construire
  des interfaces utilisateur interactives et dynamiques. Il permet de
  créer des composants réutilisables, offrant une grande modularité et
  facilitant la maintenance et l'évolution de l'application.

  \begin{enumerate}
  \def\labelenumi{\arabic{enumi}.}
  \item
    \textbf{Backend}
  \end{enumerate}
\end{itemize}

\begin{itemize}
\item
  \textbf{Laravel} : Ce Framework PHP est utilisé pour le backend grâce
  à sa robustesse et à ses fonctionnalités intégrées (ORM Eloquent,
  routes sécurisées, gestion des utilisateurs, etc.). Laravel facilite
  le développement rapide tout en assurant une sécurité optimale pour
  les transactions et les communications avec la base de données.
\item
  \textbf{Keycloak} : Utilisé pour gérer l'authentification des
  utilisateurs, Keycloak assure la sécurité de l'application en
  centralisant la gestion des utilisateurs et des sessions. Il intègre
  des mécanismes comme \textbf{OAuth 2.0} et \textbf{OpenID Connect},
  offrant une authentification sécurisée tout en facilitant
  l'intégration avec des applications externes.

  \begin{enumerate}
  \def\labelenumi{\arabic{enumi}.}
  \item
    \textbf{Base de données}
  \end{enumerate}
\end{itemize}

\begin{itemize}
\item
  \textbf{PostgreSQL} : La base de données relationnelle
  \textbf{PostgreSQL} a été choisie pour sa stabilité, sa capacité à
  gérer des transactions complexes et à stocker des données structurées
  de manière sécurisée. Elle est parfaitement adaptée pour gérer les
  informations liées aux produits, aux clients et aux commandes du
  showroom.

  \begin{enumerate}
  \def\labelenumi{\arabic{enumi}.}
  \item
    \textbf{Intelligence Artificielle (IA)}
  \end{enumerate}
\end{itemize}

\begin{itemize}
\item
  \textbf{Analyse émotionnelle par reconnaissance faciale} : Des modèles
  d'IA seront utilisés pour analyser les émotions des clients en entrant
  et en sortant du showroom physique via la reconnaissance faciale. Ces
  données permettent de mesurer la satisfaction des clients en temps
  réel, améliorant ainsi l'expérience et ajustant les recommandations.

  \begin{enumerate}
  \def\labelenumi{\arabic{enumi}.}
  \item
    \textbf{Autres technologies}
  \end{enumerate}
\end{itemize}

\begin{itemize}
\item
  \textbf{API RESTful} : La communication entre le frontend et le
  backend se fait via des API RESTful, garantissant une interaction
  rapide et sécurisée. Les API RESTful facilitent également
  l'intégration avec des services externes tels que les paiements ou les
  notifications.
\item
  \textbf{OAuth 2.0 / JWT} : Pour la gestion des utilisateurs et la
  sécurité des sessions, OAuth 2.0 et JWT (JSON Web Tokens) sont
  utilisés. Ce système permet de gérer de manière sécurisée les accès à
  l'application tout en garantissant une expérience sans interruption
  pour l'utilisateur.

  \begin{enumerate}
  \def\labelenumi{\arabic{enumi}.}
  \item
    \textbf{Schéma d'architecture}
  \end{enumerate}
\end{itemize}

\begin{itemize}
\item
  Frontend (React.js) : Interface dynamique pour l'utilisateur,
  consommant les API et bénéficiant de recommandations personnalisées.
\item
  Backend API (Laravel) : Gère les utilisateurs, les commandes et les
  produits via les API RESTful.
\item
  Keycloak : Système d'authentification centralisé pour gérer les
  utilisateurs et leur sécurité.
\item
  AI Services : Intégration des services d'IA pour l'analyse des
  émotions et les recommandations de produits.
\item
  Database (PostgreSQL) : Base de données relationnelle pour stocker les
  informations critiques.
\end{itemize}

\begin{enumerate}
\def\labelenumi{\arabic{enumi}.}
\setcounter{enumi}{3}
\item
  \begin{quote}
  \textbf{Méthodologie de travail et langage de modélisation}
  \end{quote}

  \begin{enumerate}
  \def\labelenumii{\arabic{enumii}.}
  \item
    \textbf{Méthodologie Adaptée}
  \end{enumerate}
\end{enumerate}

\begin{quote}
Le choix d'une méthodologie de développement est essentiel pour la
réussite d'un projet. Une bonne organisation, accompagnée d'un suivi
clair, facilite la gestion des différentes étapes et permet de s'adapter
aux changements. Quelle que soit la taille ou la complexité du projet,
choisir la bonne méthodologie peut vraiment influencer le résultat.

\includegraphics[width=4.42222in,height=3.62292in]{media/image1.png}

Après avoir exploré plusieurs méthodologies adaptées aux besoins et
objectifs de nos projet, on a décidé d'adopter la méthode SCRUM.
\end{quote}

\begin{enumerate}
\def\labelenumi{\arabic{enumi}.}
\item
  \textbf{SCRUM}
\end{enumerate}

\begin{quote}
SCRUM est une méthode agile de développement qui s'appuie sur des cycles
courts et itératifs appelés sprints. Cette approche se distingue par sa
flexibilité et son efficacité, permettant de livrer des fonctionnalités
à la fin de chaque sprint. Ce qui rend Scrum particulièrement
intéressant, c'est sa capacité à s'adapter rapidement aux besoins des
clients grâce à une communication ouverte et un processus d'amélioration
continue. L'objectif principal est de transformer les "user stories" en
un "product backlog", une liste de priorités organisée par ordre
d'importance. Au début de chaque sprint, l'équipe Scrum sélectionne les
éléments les plus prioritaires pour former le "sprint backlog", un
ensemble de tâches à réaliser dans un délai défini.

\includegraphics[width=6.3in,height=2.90764in]{media/image3.png}Le
schéma ci-dessous illustre les différentes étapes du processus Scrum :
\end{quote}

Scrum offre de nombreux avantages par rapport à d'autres méthodologies
agiles, faisant de lui l'un des cadres les plus adoptés et appréciés
dans l'industrie du logiciel. Voici les principaux atouts de Scrum :

\begin{itemize}
\item
  \textbf{Flexibilité et adaptabilité :} Grâce à sa structure itérative,
  Scrum permet de réajuster les priorités et les exigences en fonction
  des retours d'expérience et des changements dans l'environnement,
  assurant ainsi une meilleure adéquation avec les besoins des clients.
\item
  \textbf{Renforcement de la collaboration :} Les réunions quotidiennes
  (daily stand-ups) et les interactions fréquentes entre les membres de
  l'équipe encouragent une communication ouverte et un travail d'équipe
  efficace.
\item
  \textbf{Réduction du temps de mise sur le marché :} En livrant
  régulièrement des incréments fonctionnels du produit, Scrum accélère
  la mise à disposition des fonctionnalités et facilite un retour rapide
  des utilisateurs.
\item
  \textbf{Amélioration continue :} Les rétrospectives de sprint offrent
  un espace dédié pour évaluer les processus et identifier des
  opportunités d'amélioration, permettant ainsi aux équipes d'optimiser
  leur productivité au fil du temps.

  \begin{enumerate}
  \def\labelenumi{\arabic{enumi}.}
  \item
    \textbf{Backlog du produit}
  \end{enumerate}
\end{itemize}

Tableau 1~: Planification des Sprints pour le projet

\begin{longtable}[]{@{}ll@{}}
\toprule
\textbf{Sprints} & \textbf{Descriptions}\tabularnewline
\midrule
\endhead
\begin{minipage}[t]{0.47\columnwidth}\raggedright
Sprint 1 : Mise en place de l'infrastructure\strut
\end{minipage} & \begin{minipage}[t]{0.47\columnwidth}\raggedright
\begin{longtable}[]{@{}l@{}}
\toprule
\endhead
\begin{minipage}[t]{0.97\columnwidth}\raggedright
\begin{itemize}
\item
  Mise en place de l'architecture technique (React.js, Laravel,
  PostgreSQL).
\end{itemize}\strut
\end{minipage}\tabularnewline
\bottomrule
\end{longtable}

\begin{longtable}[]{@{}ll@{}}
\toprule
\endhead
\begin{minipage}[t]{0.47\columnwidth}\raggedright
\begin{itemize}
\tightlist
\item
\end{itemize}\strut
\end{minipage} & \begin{minipage}[t]{0.47\columnwidth}\raggedright
\begin{itemize}
\item
  Intégration de Keycloak pour la gestion des utilisateurs (clients,
  administrateurs).
\end{itemize}\strut
\end{minipage}\tabularnewline
\bottomrule
\end{longtable}

\begin{longtable}[]{@{}ll@{}}
\toprule
\endhead
\begin{minipage}[t]{0.47\columnwidth}\raggedright
\begin{itemize}
\tightlist
\item
\end{itemize}\strut
\end{minipage} & \begin{minipage}[t]{0.47\columnwidth}\raggedright
\begin{itemize}
\item
  Développement de l'interface d'authentification (login, inscription).
\end{itemize}\strut
\end{minipage}\tabularnewline
\bottomrule
\end{longtable}

\begin{longtable}[]{@{}ll@{}}
\toprule
\endhead
\begin{minipage}[t]{0.47\columnwidth}\raggedright
\begin{itemize}
\tightlist
\item
\end{itemize}\strut
\end{minipage} & \begin{minipage}[t]{0.47\columnwidth}\raggedright
\begin{itemize}
\item
  Création du module de gestion des produits.
\end{itemize}\strut
\end{minipage}\tabularnewline
\bottomrule
\end{longtable}

\begin{longtable}[]{@{}lll@{}}
\toprule
\endhead
\begin{minipage}[t]{0.30\columnwidth}\raggedright
\begin{itemize}
\tightlist
\item
\end{itemize}\strut
\end{minipage} & \begin{minipage}[t]{0.30\columnwidth}\raggedright
\begin{itemize}
\tightlist
\item
\end{itemize}\strut
\end{minipage} & \begin{minipage}[t]{0.30\columnwidth}\raggedright
\begin{itemize}
\item
  Mise en place de l'API RESTful pour les produits et les utilisateurs.
\end{itemize}\strut
\end{minipage}\tabularnewline
\bottomrule
\end{longtable}\strut
\end{minipage}\tabularnewline
\begin{minipage}[t]{0.47\columnwidth}\raggedright
Sprint 2 : Développement du site e-commerce\strut
\end{minipage} & \begin{minipage}[t]{0.47\columnwidth}\raggedright
\begin{longtable}[]{@{}l@{}}
\toprule
\endhead
\begin{minipage}[t]{0.97\columnwidth}\raggedright
\begin{itemize}
\item
  Développement de l'interface utilisateur (React.js) : catalogue de
  produits avec filtres (marque, prix, catégorie).
\end{itemize}\strut
\end{minipage}\tabularnewline
\bottomrule
\end{longtable}

\begin{longtable}[]{@{}ll@{}}
\toprule
\endhead
\begin{minipage}[t]{0.47\columnwidth}\raggedright
\begin{itemize}
\tightlist
\item
\end{itemize}\strut
\end{minipage} & \begin{minipage}[t]{0.47\columnwidth}\raggedright
\begin{itemize}
\item
  Mise en place du panier d'achat et système de commande.
\end{itemize}\strut
\end{minipage}\tabularnewline
\bottomrule
\end{longtable}

\begin{longtable}[]{@{}ll@{}}
\toprule
\endhead
\begin{minipage}[t]{0.47\columnwidth}\raggedright
\begin{itemize}
\tightlist
\item
\end{itemize}\strut
\end{minipage} & \begin{minipage}[t]{0.47\columnwidth}\raggedright
\begin{itemize}
\item
  Intégration d'un système de paiement sécurisé.
\end{itemize}\strut
\end{minipage}\tabularnewline
\bottomrule
\end{longtable}

\begin{longtable}[]{@{}ll@{}}
\toprule
\endhead
\begin{minipage}[t]{0.47\columnwidth}\raggedright
\begin{itemize}
\tightlist
\item
\end{itemize}\strut
\end{minipage} & \begin{minipage}[t]{0.47\columnwidth}\raggedright
\begin{itemize}
\item
  Création de la page de profil utilisateur.
\item
  Développement d'un chatbot statique
\end{itemize}\strut
\end{minipage}\tabularnewline
\bottomrule
\end{longtable}\strut
\end{minipage}\tabularnewline
&\tabularnewline
\begin{minipage}[t]{0.47\columnwidth}\raggedright
Sprint 4 : Intégration de l'IA pour la reconnaissance faciale\strut
\end{minipage} & \begin{minipage}[t]{0.47\columnwidth}\raggedright
\begin{itemize}
\item
  Déploiement du module de reconnaissance faciale pour l'analyse des
  émotions (satisfaction client).
\end{itemize}\strut
\end{minipage}\tabularnewline
\begin{minipage}[t]{0.47\columnwidth}\raggedright
Sprint 5 : Tests, déploiement et finalisation\strut
\end{minipage} & \begin{minipage}[t]{0.47\columnwidth}\raggedright
\begin{itemize}
\item
  Tests de charge et optimisation des performances
\item
  Correction des bugs et validation des fonctionnalités.
\item
  Déploiement du site web.
\end{itemize}\strut
\end{minipage}\tabularnewline
\bottomrule
\end{longtable}

\begin{enumerate}
\def\labelenumi{\arabic{enumi}.}
\item
  \textbf{Langage de modélisation}
\end{enumerate}

\begin{quote}
Conçu comme un langage de modélisation visuelle universel, l'UML
(Unified Modeling Langages) offre un riche vocabulaire graphique pour
représenter de manière précise les systèmes logiciels. Grâce à sa
diversité de diagrammes, il permet de visualiser les différents aspects
d'un système : sa structure statique, ses comportements dynamiques, et
ses interactions. Pour ce projet, on a choisi l'UML comme langage de
modélisation car il offre une représentation visuelle de la solution. Il
nous permet clarifier les besoins des utilisateurs et d'optimiser le
développement de l'application, garantissant qu'elle réponde bien aux
attentes des candidats et des agents des ressources humaines.

\includegraphics[width=4.46944in,height=2.16875in]{media/image5.png}
\end{quote}

\textbf{Chapitre 3 : Sprint 1~-- Mise en place de l'infrastructure}

\begin{enumerate}
\def\labelenumi{\arabic{enumi}.}
\item
  \textbf{Introduction}
\end{enumerate}

Ce chapitre présente le premier sprint de notre projet, consacré à la
mise en place de l'infrastructure technique, pilier fondamental du site
e-commerce. Ce sprint vise à construire une base stable et scalable
intégrant les technologies modernes, la sécurité, et les services de
base (authentification, produits).

\begin{enumerate}
\def\labelenumi{\arabic{enumi}.}
\setcounter{enumi}{1}
\item
  \textbf{Objectifs du sprint}
\end{enumerate}

L'objectif général du sprint est de \textbf{construire l'ossature
technique} du site web, incluant :

\textbf{Architecture technique} :

\begin{itemize}
\item
  Configurer le front-end en React.js, le back-end avec Laravel, et la
  base de données PostgreSQL.
\item
  Mettre en place l'interconnexion entre ces composants pour assurer la
  fluidité des échanges.
\end{itemize}

\textbf{Sécurisation et gestion des utilisateurs (Keycloak)} :

\begin{itemize}
\item
  Intégrer Keycloak comme système central d'authentification et
  d'autorisation.
\item
  Définir les rôles (clients, administrateurs) et les permissions
  associées.
\item
  Configurer la communication sécurisée avec les API backend.
\end{itemize}

\textbf{Interface d'authentification} :

\begin{itemize}
\item
  Créer les écrans de connexion et d'inscription.
\item
  Gérer l'envoi et la vérification des informations utilisateur via
  Keycloak et l'API.
\end{itemize}

\textbf{Module de gestion des produits} :

\begin{itemize}
\item
  Définir la structure des entités produit dans la base de données.
\item
  Créer les endpoints RESTful pour la gestion des produits (CRUD).
\item
  Établir la connexion entre le front-end et le backend pour l'affichage
  et la modification des produits.
\end{itemize}

\textbf{Mise en place de l'API RESTful} :

\begin{itemize}
\item
  Créer des endpoints sécurisés pour la gestion des utilisateurs et des
  produits.
\item
  Définir les routes et contrôleurs dans Laravel.
\item
  Assurer la compatibilité avec le front-end React.js.

  \begin{enumerate}
  \def\labelenumi{\arabic{enumi}.}
  \item
    \textbf{Conception}
  \end{enumerate}

  \begin{enumerate}
  \def\labelenumi{\arabic{enumi}.}
  \item
    \textbf{Diagramme de composants}
  \end{enumerate}
\end{itemize}

Ce diagramme illustre la structure technique du système.

\begin{itemize}
\item
  \textbf{Frontend (React.js)} \textbf{:} interface utilisateur qui
  communique avec le Backend via des appels HTTP et s'authentifie via
  Keycloak.
\item
  \textbf{Backend (Laravel)} \textbf{:} logique métier, expose les API
  RESTful, interagit avec la base PostgreSQL et valide les tokens
  d'authentification.
\item
  \textbf{Base de données (PostgreSQL)} \textbf{:} stocke les données
  persistantes de l'application.
\item
  \textbf{Keycloak} \textbf{:} gère l'authentification, l'autorisation
  et les sessions utilisateurs.
\end{itemize}

\includegraphics[width=6.3in,height=3.25in]{media/image7.png}Le
diagramme montre les échanges principaux entre les composants pour une
meilleure compréhension de l'architecture globale.

\begin{enumerate}
\def\labelenumi{\arabic{enumi}.}
\item
  \textbf{Diagramme cas d'utilisation}
\end{enumerate}

Ce diagramme présente les principales interactions entre les acteurs et
le système e-commerce.

\begin{itemize}
\item
  \textbf{Client :} peut s'inscrire, se connecter (via Keycloak),
  consulter le catalogue, passer commande, effectuer un paiement et
  accéder à son profil utilisateur.
\item
  \textbf{Administrateur :} peut s'inscrire, se connecter (via
  Keycloak), consulter le catalogue, gérer les produits et accéder à son
  profil.
\end{itemize}

Ce diagramme met en évidence les cas d'utilisation principaux et montre
comment les utilisateurs interagissent avec le système.

\includegraphics[width=6.3in,height=1.88472in]{media/image9.png}

\begin{enumerate}
\def\labelenumi{\arabic{enumi}.}
\item
  \textbf{Diagramme de classe}
\end{enumerate}

Ce diagramme de classes montre la structure principale du système,
centrée sur les entités et leurs relations :

\begin{itemize}
\item
  \textbf{User :} représente les utilisateurs avec leurs informations et
  actions principales.
\item
  \textbf{Client :} héritant de User, avec adresse et téléphone pour la
  gestion des commandes.
\item
  \textbf{Produit :} décrit les articles vendus, avec prix, quantité et
  description.
\item
  \textbf{Commande :} regroupe les détails d'une commande, y compris le
  client et le montant.
\item
  \textbf{CommandeProduit :} relie les produits aux commandes avec la
  quantité et le prix.
\end{itemize}

Les relations montrent que\,:

\begin{itemize}
\item
  Un utilisateur peut avoir plusieurs commandes et produits (en tant
  qu'admin).
\item
  Un client peut passer plusieurs commandes.
\item
  Une commande peut contenir plusieurs produits.
\end{itemize}

\includegraphics[width=4.50694in,height=3.94861in]{media/image11.png}

\begin{enumerate}
\def\labelenumi{\arabic{enumi}.}
\item
  \textbf{Diagramme de séquence}
\end{enumerate}

\begin{itemize}
\item
  Ce diagramme montre le flux d'authentification et de récupération du
  profil utilisateur.
\item
  L'utilisateur saisit ses identifiants sur le frontend (React), qui les
  envoie à Keycloak pour vérification.
\item
  Keycloak renvoie un token d'accès que le frontend utilise pour
  interroger le backend (Laravel).
\item
  Le backend valide le token auprès de Keycloak et récupère les rôles et
  informations utilisateur.
\item
  Le backend interagit avec la base de données PostgreSQL pour
  synchroniser l'utilisateur et renvoie le profil complet au frontend.
\item
  Le frontend affiche enfin le tableau de bord utilisateur.
\end{itemize}

\includegraphics[width=6.3in,height=3.35278in]{media/image13.png}Ce
diagramme met en évidence les échanges techniques entre les composants
du système lors du processus d'authentification.

\begin{enumerate}
\def\labelenumi{\arabic{enumi}.}
\item
  \textbf{Réalisation}
\end{enumerate}

\begin{enumerate}
\def\labelenumi{\arabic{enumi}.}
\item
  \textbf{Frontend}
\end{enumerate}

Le frontend de notre application est développé à l'aide de React.js, une
bibliothèque JavaScript conçue pour la création d'interfaces utilisateur
interactives et dynamiques. L'architecture de ce frontend est basée sur
une approche modulaire, où l'interface est construite à partir de
composants réutilisables. Cette conception facilite la maintenance,
l'évolution et l'extensibilité de l'application.

\begin{enumerate}
\def\labelenumi{\arabic{enumi}.}
\item
  \textbf{Principaux Composants et Fonctionnalités}
\end{enumerate}

\begin{itemize}
\item
  \includegraphics[width=5.62014in,height=3.35833in]{media/image15.png}\includegraphics[width=5.62917in,height=3.37639in]{media/image17.png}\textbf{Authentification
  Intégrée :} L'authentification des utilisateurs (connexion et
  inscription) est gérée au sein de composants dédiés à chaque page ou
  section nécessitant une authentification. Cette approche permet une
  intégration plus étroite avec le flux de l'application et une
  meilleure adaptation aux besoins spécifiques de chaque contexte.
  L'interaction avec l'API d'authentification de Keycloak pour la
  récupération des tokens d'accès est assurée par des fonctions ou des
  services d'authentification, utilisés par ces composants.
\end{itemize}

\begin{itemize}
\item
  \textbf{Liste des Produits (ProductsPage) :} Ce composant prend en
  charge l'affichage du catalogue des produits. Il offre des
  fonctionnalités de filtrage, de tri et de pagination pour faciliter la
  navigation des utilisateurs à travers les différentes références. La
  récupération des données produits est effectuée via l'API dédiée.
\item
  \textbf{Détail d'un Produit (ProductDetailPage)} : Ce composant
  affiche les informations complètes d'un produit spécifique, permettant
  aux utilisateurs d'obtenir une vue détaillée avant de prendre une
  décision d'achat.

  \begin{enumerate}
  \def\labelenumi{\arabic{enumi}.}
  \item
    \textbf{Gestion de l'État et des Interactions}
  \end{enumerate}
\end{itemize}

Pour assurer une gestion efficace des données et des interactions au
sein de l'application, nous avons utilisé React Context. React Context
est employé pour la gestion de l'état global, notamment pour les
informations liées à l'authentification et à l'utilisateur courant. Les
appels à l'API backend sont réalisés à l'aide de la bibliothèque Axios.
Afin de garantir la sécurité des échanges, les tokens d'authentification
sont systématiquement inclus dans les en-têtes des requêtes HTTP.

\begin{enumerate}
\def\labelenumi{\arabic{enumi}.}
\item
  \textbf{Backend}
\end{enumerate}

Le backend de notre application est développé à l'aide de Laravel, un
framework PHP open-source robuste et largement adopté. Laravel offre une
structure et des outils qui simplifient le développement d'applications
web complexes, tout en favorisant les bonnes pratiques de développement.

\begin{enumerate}
\def\labelenumi{\arabic{enumi}.}
\item
  \textbf{Architecture et Logique Métier}
\end{enumerate}

\begin{itemize}
\item
  \textbf{Structure des Routes et des Contrôleurs :}

  \begin{itemize}
  \item
    Les routes de l'API RESTful sont définies dans le fichier
    routes/api.php. Cela permet d'organiser clairement les points
    d'accès de l'API et de les associer aux actions correspondantes.
  \item
    Les contrôleurs (ProduitController, UserController,
    KeycloakVerificationController...) jouent un rôle central dans le
    backend. Ils contiennent la logique métier de l'application,
    traitant les requêtes HTTP et orchestrant les interactions avec les
    modèles et les services.
  \end{itemize}
\item
  \textbf{Modèle Eloquent et Interaction avec la Base de Données :}

  \begin{itemize}
  \item
    Laravel utilise Eloquent ORM (Object-Relational Mapping) pour
    faciliter l'interaction avec la base de données. Eloquent permet de
    manipuler les données de la base de données en utilisant une syntaxe
    orientée objet, ce qui rend le code plus lisible et maintenable.
  \item
    Les modèles (Product, User, Order) représentent les tables de la
    base de données. Chaque modèle correspond à une table et définit les
    propriétés et les relations entre les données.
  \item
    La base de données utilisée est PostgreSQL, choisie pour sa
    robustesse, sa gestion des transactions et sa conformité aux normes
    SQL.

    \begin{enumerate}
    \def\labelenumi{\arabic{enumi}.}
    \item
      \textbf{Sécurité et Authentification}
    \end{enumerate}
  \end{itemize}
\end{itemize}

\begin{itemize}
\item
  \textbf{Protection des Routes API :}

  \begin{itemize}
  \item
    Le middleware auth:api de Laravel est utilisé pour protéger les
    routes de l'API. Ce middleware assure que seules les requêtes
    authentifiées peuvent accéder aux ressources sensibles.
  \end{itemize}
\item
  \textbf{Validation des Tokens Keycloak :}

  \begin{itemize}
  \item
    L'authentification est gérée par Keycloak, un système d'identité et
    d'accès.
  \item
    La validation des tokens Keycloak est effectuée à l'aide de la
    bibliothèque firebase/php-jwt. Cette bibliothèque permet de décoder
    et de vérifier l'intégrité des tokens JWT (JSON Web Tokens) émis par
    Keycloak, garantissant ainsi que les requêtes sont bien autorisées.
  \end{itemize}

  \begin{enumerate}
  \def\labelenumi{\arabic{enumi}.}
  \item
    \textbf{Base de données (PostgreSQL)}

    \begin{enumerate}
    \def\labelenumii{\arabic{enumii}.}
    \item
      \textbf{PostgreSQL}
    \end{enumerate}
  \end{enumerate}
\end{itemize}

PostgreSQL a été choisi comme système de gestion de base de données en
raison de sa robustesse, de sa conformité aux normes SQL et de ses
performances, notamment en termes de gestion des transactions et de
support des types de données avancés. Cette décision assure la fiabilité
et la scalabilité nécessaires pour supporter la croissance future de
l'application.

\begin{enumerate}
\def\labelenumi{\arabic{enumi}.}
\setcounter{enumi}{1}
\item
  \textbf{Migrations Laravel :}
\end{enumerate}

Les migrations Laravel ont été utilisées pour créer et gérer le schéma
de la base de données. Cela offre plusieurs avantages :

\begin{itemize}
\item
  \textbf{Contrôle de version :} Les modifications du schéma sont
  versionnées, facilitant le suivi et la gestion des changements.
\item
  \textbf{Portabilité :} Les migrations permettent de recréer la base de
  données sur différents environnements (développement, test,
  production) de manière cohérente.
\item
  \textbf{Automatisation :} Les migrations automatisent le processus de
  création et de mise à jour du schéma, réduisant les erreurs manuelles.

  \begin{enumerate}
  \def\labelenumi{\arabic{enumi}.}
  \item
    \textbf{Keycloak}
  \end{enumerate}
\end{itemize}

Keycloak a été intégré pour la gestion de l'authentification et de
l'autorisation, offrant une solution centralisée et sécurisée. Cette
approche permet de déléguer la gestion des identités à un système dédié,
améliorant ainsi la sécurité et simplifiant le développement.

\begin{enumerate}
\def\labelenumi{\arabic{enumi}.}
\item
  \textbf{Configuration de Keycloak :}
\end{enumerate}

\begin{itemize}
\item
  Realm : Un "realm" nommé "jiheneline" a été créé pour isoler les
  utilisateurs et les applications de notre projet. Les realms
  permettent de gérer plusieurs applications et leurs utilisateurs de
  manière indépendante au sein d'une même instance Keycloak.
\item
  Clients : Trois "clients" ont été configurés :

  \begin{itemize}
  \item
    backoffice-client~: Représente l'application frontend (React.js)
    pour l'administrateur. Il est configuré pour utiliser le flow
    d'authentification "Authorization Code Flow" avec PKCE (Proof Key
    for Code Exchange) pour une sécurité accrue, notamment pour les
    applications monopages. Les URLs de redirection et de déconnexion
    sont configurées pour pointer vers l'application frontend.
  \item
    frontoffice-client : Représente l'application frontend (React.js)
    pour les clients. Il est configuré pour utiliser le flow
    d'authentification "Authorization Code Flow" avec PKCE (Proof Key
    for Code Exchange) pour une sécurité accrue, notamment pour les
    applications monopages. Les URLs de redirection et de déconnexion
    sont configurées pour pointer vers l'application frontend.
  \item
    api-client : Représente l'application backend (Laravel). Il est
    configuré comme un client "confidential" avec un "client secret"
    pour sécuriser les échanges. Il est utilisé pour valider les tokens
    d'accès fournis par le frontend.
  \end{itemize}
\item
  Rôles : Les rôles "client" et "administrateur" ont été définis au
  niveau du realm. Ces rôles permettent de contrôler l'accès aux
  différentes ressources et fonctionnalités de l'application.

  \begin{itemize}
  \item
    Client : Les utilisateurs avec ce rôle ont accès aux fonctionnalités
    de base du site e-commerce, telles que la consultation du catalogue,
    la passation de commandes et la gestion de leur profil.
  \item
    Administrateur : Les utilisateurs avec ce rôle ont des privilèges
    élevés, leur permettant de gérer les produits, les commandes, les
    utilisateurs et d'autres aspects administratifs du site.
  \end{itemize}
\item
  Protocoles : Les protocoles OAuth 2.0 et OpenID Connect sont utilisés
  pour l'authentification et l'autorisation.

  \begin{itemize}
  \item
    OAuth 2.0 : Est utilisé pour l'autorisation, permettant à
    l'application frontend d'accéder aux ressources protégées du backend
    au nom de l'utilisateur.
  \item
    OpenID Connect : Est une couche d'authentification construite
    au-dessus d'OAuth 2.0, fournissant des informations d'identité sur
    l'utilisateur authentifié (par exemple, le nom, l'email).

    \begin{enumerate}
    \def\labelenumi{\arabic{enumi}.}
    \item
      \textbf{Intégration avec le frontend et le backend :}
    \end{enumerate}
  \end{itemize}
\end{itemize}

\begin{itemize}
\item
  Frontend (React.js) :

  \begin{itemize}
  \item
    La bibliothèque keycloak-js est utilisée pour faciliter
    l'intégration avec Keycloak. Elle permet de gérer
    l'authentification, la récupération des tokens d'accès et la gestion
    des sessions utilisateur.
  \item
    Le frontend initialise keycloak-js au démarrage de l'application,
    établissant une connexion avec le serveur Keycloak.
  \item
    Les utilisateurs sont redirigés vers la page de connexion de
    Keycloak pour s'authentifier.
  \item
    Après une authentification réussie, Keycloak redirige l'utilisateur
    vers l'application frontend avec un code d'autorisation.
  \item
    Le frontend échange ce code contre un token d'accès et un token
    d'actualisation.
  \item
    Le token d'accès est inclus dans les en-têtes des requêtes HTTP vers
    le backend pour autoriser l'accès aux ressources protégées.
  \item
    Le token d'actualisation est utilisé pour obtenir de nouveaux tokens
    d'accès sans nécessiter une nouvelle authentification de
    l'utilisateur.
  \item
    L'application frontend utilise les informations du token d'accès
    pour afficher les informations de l'utilisateur et contrôler l'accès
    aux fonctionnalités en fonction du rôle de l'utilisateur.
  \end{itemize}
\item
  Backend (Laravel) :

  \begin{itemize}
  \item
    Le backend valide les tokens d'accès fournis par le frontend pour
    s'assurer que les requêtes sont autorisées.
  \item
    Plusieurs bibliothèques et méthodes peuvent être utilisées pour la
    validation des tokens, notamment :

    \begin{itemize}
    \item
      firebase/php-jwt : Une bibliothèque PHP qui permet de décoder et
      de vérifier les tokens JWT (JSON Web Tokens).
    \item
      Intégration directe avec l'API Keycloak : Laravel peut également
      communiquer directement avec l'API Keycloak pour valider les
      tokens.
    \end{itemize}
  \item
    Le backend extrait les informations de l'utilisateur (par exemple,
    le rôle) du token d'accès pour autoriser l'accès aux ressources et
    aux actions en fonction des permissions de l'utilisateur.
  \item
    Le middleware auth:api de Laravel est utilisé pour protéger les
    routes de l'API, assurant que seules les requêtes authentifiées
    peuvent accéder aux ressources sensibles.
  \end{itemize}

  \begin{enumerate}
  \def\labelenumi{\arabic{enumi}.}
  \item
    \textbf{Conclusion}
  \end{enumerate}
\end{itemize}

Ce premier sprint a permis de mettre en place l'infrastructure technique
du projet, avec la configuration de Keycloak, la mise en place du
backend Laravel et PostgreSQL, et la création du frontend React. Ces
étapes ont posé les bases solides pour la suite du projet et assuré une
bonne communication entre les différents modules.

\textbf{\hfill\break
Chapitre 4 : Sprint 2 -- Développement du site e-commerce}

\begin{enumerate}
\def\labelenumi{\arabic{enumi}.}
\item
  \textbf{Introduction}
\end{enumerate}

Ce chapitre présente le deuxième sprint de notre projet, axé sur la
création du site e-commerce. L'objectif est de concevoir et de
développer les fonctionnalités principales de l'application : navigation
dans le catalogue, gestion du panier, finalisation des commandes et mise
en place d'un système de paiement sécurisé. Ce sprint consolide
l'expérience utilisateur et pose les bases des interactions
commerciales.

\begin{enumerate}
\def\labelenumi{\arabic{enumi}.}
\setcounter{enumi}{1}
\item
  \textbf{Objectifs du sprint}
\end{enumerate}

L'objectif général est de transformer l'infrastructure technique
existante en une plateforme e-commerce fonctionnelle.\\
Les objectifs détaillés incluent\,:

\begin{itemize}
\item
  \textbf{Catalogue de produits avec filtres et recherche :} permettre
  aux utilisateurs de visualiser les produits par marque, prix,
  catégorie.
\item
  \textbf{Panier d'achat et commandes :} mise en place du panier avec
  ajout/suppression de produits, calcul automatique des totaux et taxes.
\item
  \textbf{Système de paiement sécurisé :} intégration d'une solution de
  paiement (ex\,: Stripe, PayPal) avec retour de confirmation et
  validation des commandes.
\item
  \textbf{Profil utilisateur :} affichage et modification des
  informations personnelles, visualisation des commandes passées.
\item
  \textbf{Chatbot statique :} développement d'un assistant
  conversationnel rudimentaire capable de répondre à des questions
  simples (FAQ, contact).

  \begin{enumerate}
  \def\labelenumi{\arabic{enumi}.}
  \item
    \textbf{Conception}
  \end{enumerate}

  \begin{enumerate}
  \def\labelenumi{\arabic{enumi}.}
  \item
    \textbf{Diagramme cas d'utilisation}
  \end{enumerate}
\end{itemize}

Ce diagramme illustre les interactions entre les acteurs (Client,
Administrateur) et le système e-commerce.\\
Il met en évidence les cas d'utilisation principaux, tels que :

\begin{itemize}
\item
  \textbf{Authentification et inscription} via Keycloak.
\item
  \textbf{Consultation, filtrage et recherche} des produits.
\item
  \textbf{Gestion du panier} (ajout, suppression).
\item
  \textbf{Passage de commande} et \textbf{paiement sécurisé}.
\item
  \textbf{Accès et modification du profil utilisateur}.
\item
  \textbf{Utilisation du chatbot (FAQ)} pour obtenir des réponses
  automatiques.
\item
  \textbf{Administration des produits} pour l'administrateur.
\end{itemize}

\includegraphics[width=3.97708in,height=6.52431in]{media/image19.png}

Ce diagramme montre clairement les différentes fonctionnalités offertes
par le système et les interactions attendues selon le rôle de
l'utilisateur.

\begin{enumerate}
\def\labelenumi{\arabic{enumi}.}
\item
  \textbf{Diagramme de séquence}
\end{enumerate}

Ce diagramme illustre le flux d'une commande complète, depuis la
sélection des produits par le client jusqu'à la confirmation finale de
paiement.\\
Il met en évidence les interactions principales entre\,:

\begin{itemize}
\item
  le \textbf{Client},
\item
  le \textbf{Frontend React} (interface utilisateur),
\item
  le \textbf{Backend Laravel} (gestion logique et API),
\item
  le \textbf{Service Paiement} (Stripe),
\item
  et la \textbf{base de données PostgreSQL} (persistance des commandes).
\end{itemize}

Les étapes clés comprennent\,:

\begin{itemize}
\item
  La \textbf{validation du panier} et la \textbf{création de la
  commande} (initialement en attente).
\item
  L'\textbf{interaction sécurisée avec le service de paiement}
  (confirmation ou échec).
\item
  La \textbf{mise à jour du statut de la commande} dans la base de
  données en fonction du résultat de la transaction.
\item
  La \textbf{confirmation finale} envoyée au client avec un reçu.
\end{itemize}

\includegraphics[width=5.175in,height=3.18264in]{media/image21.png}

Ce diagramme montre clairement comment le système gère le processus
complet de commande, garantissant une expérience sécurisée et cohérente
pour l'utilisateur.

\begin{enumerate}
\def\labelenumi{\arabic{enumi}.}
\item
  \textbf{Diagramme de classe}
\end{enumerate}

\includegraphics[width=5.38611in,height=4.28958in]{media/image23.png}

\begin{enumerate}
\def\labelenumi{\arabic{enumi}.}
\item
  \textbf{Réalisation}
\end{enumerate}

Ce sprint a été consacré au développement des deux composantes
principales de la plateforme e-commerce~: le site côté client et le site
côté administrateur. Les technologies utilisées sont restées celles
définies lors du Sprint 1, à savoir React.js pour le frontend et Laravel
pour le backend, avec PostgreSQL comme base de données.

\begin{enumerate}
\def\labelenumi{\arabic{enumi}.}
\item
  \textbf{Site Côté Client}
\end{enumerate}

Le site côté client a été conçu pour offrir une expérience utilisateur
riche et intuitive, centrée sur la découverte des produits et la
facilité de commande.

\begin{itemize}
\item
  Affichage des Produits~:

  \begin{itemize}
  \item
    L'affichage des produits a été structuré en respectant
    l'arborescence Catégorie \textgreater{} Sous-Catégorie
    \textgreater{} Sous-Sous-Catégorie, permettant une navigation fluide
    et précise.
  \item
    Des composants React.js ont été créés pour chaque niveau de
    catégorie, exploitant les relations entre les tables Categorie,
    SousCategorie et SousSousCategorie définies dans le diagramme de
    classes.
  \item
    Chaque produit (Produit) est affiché avec ses informations
    essentielles (nom, prix, image) et les liens vers les pages de
    détail.
  \item
    Les marques (Marque) sont intégrées comme filtres et sont liées aux
    produits via la relation Produit -\textgreater{} Marque.
  \end{itemize}
\item
  Gestion des Variantes~:

  \begin{itemize}
  \item
    Les produits avec variantes (par exemple, taille, couleur) utilisent
    le modèle ProduitVariante pour afficher les options disponibles et
    gérer le stock.
  \item
    Le prix final est calculé en tenant compte du prix de base du
    produit et du prix supplémentaire de la variante (prix\_supplement).
  \end{itemize}
\item
  Panier et Commande~:

  \begin{itemize}
  \item
    Le panier est géré via le modèle Panier et les articles du panier
    via PanierItem, permettant aux clients d'ajouter, modifier et
    supprimer des produits.
  \item
    Le processus de commande utilise les modèles Commande et
    CommandeProduit pour enregistrer les informations de la commande et
    les détails des produits commandés.
  \item
    L'adresse de livraison et les informations de contact sont issues du
    modèle Client (lié à User).
  \end{itemize}
\item
  Chatbot Statique~:

  \begin{itemize}
  \item
    Un composant de chatbot statique a été intégré pour répondre aux
    questions fréquentes (FAQ), améliorant l'assistance client.
  \item
    Les réponses sont prédéfinies et stockées dans un fichier JSON ou
    une table de base de données simple.
  \end{itemize}
\item
  Système de Recommandation~:

  \begin{itemize}
  \item
    Un système de recommandation basique a été implémenté en React,
    affichant des produits similaires basés sur la catégorie ou les
    produits consultés récemment.
  \item
    Ce système utilise les relations entre les produits et les
    catégories, ainsi que le stockage local des historiques de
    navigation.
  \end{itemize}
\item
  Collections et Promotions~:

  \begin{itemize}
  \item
    Les pages de collections et de promotions affichent les produits
    liés aux modèles Collection et Promotion.
  \item
    Les relations entre ces modèles et Produit permettent de filtrer et
    d'afficher les produits concernés.
  \end{itemize}

  \begin{enumerate}
  \def\labelenumi{\arabic{enumi}.}
  \item
    \textbf{Site Côté Administrateur}
  \end{enumerate}
\end{itemize}

Le site côté administrateur a été développé pour offrir une gestion
efficace du catalogue, des commandes et du contenu du site.

\begin{itemize}
\item
  Gestion des Produits~:

  \begin{itemize}
  \item
    Les administrateurs peuvent ajouter, modifier et supprimer des
    produits via des formulaires qui interagissent avec le modèle
    Produit.
  \item
    La gestion des variantes se fait via des formulaires imbriqués qui
    permettent de créer et modifier les enregistrements dans
    ProduitVariante et VarianteValeur.
  \item
    Les attributs des produits (par exemple, couleur, matériau) sont
    gérés via les modèles Attribut et ProduitValeur, permettant de
    définir et d'assigner des caractéristiques aux produits.
  \end{itemize}
\item
  Gestion des Commandes et Factures~:

  \begin{itemize}
  \item
    Les commandes (Commande) sont affichées avec leur statut, les
    informations du client et les détails des produits commandés
    (CommandeProduit).
  \item
    Les factures peuvent être générées à partir des commandes, affichant
    le total, les remises et les informations de paiement (Paiement).
  \end{itemize}
\item
  Personnalisation du Site~:

  \begin{itemize}
  \item
    Les administrateurs peuvent personnaliser les carrousels et le
    placement des éléments sur le site via des interfaces qui mettent à
    jour les données de configuration (par exemple, l'ordre des produits
    en vedette, les images des carrousels).
  \item
    La gestion des images se fait via le modèle Image et les relations
    polymorphiques, permettant d'associer des images à différents types
    d'entités (produits, catégories, etc.).
  \end{itemize}
\item
  Formulaires d'Ajout~:

  \begin{itemize}
  \item
    Des formulaires dédiés permettent d'ajouter de nouveaux produits,
    catégories (Categorie, SousCategorie, SousSousCategorie), variantes
    et autres entités du système.
  \item
    Ces formulaires valident les données saisies et les enregistrent
    dans les tables correspondantes.

    \begin{enumerate}
    \def\labelenumi{\arabic{enumi}.}
    \item
      \textbf{Conclusion}
    \end{enumerate}
  \end{itemize}
\end{itemize}

Ce deuxième sprint a permis de transformer l'ossature initiale de notre
projet en une véritable plateforme e-commerce fonctionnelle. Nous avons
mis en œuvre les fonctionnalités principales nécessaires à l'expérience
utilisateur, depuis la navigation dans le catalogue jusqu'à la
finalisation des commandes et le paiement sécurisé. La conception du
site côté client a été guidée par une approche orientée utilisateur,
favorisant l'exploration intuitive des produits et une gestion fluide du
panier.

Le site côté administrateur, quant à lui, a été pensé pour garantir une
gestion simple et efficace du contenu et des commandes, avec des outils
d'administration adaptés aux besoins du projet. L'intégration du chatbot
statique et du système de recommandation a renforcé l'interactivité et
la personnalisation, améliorant encore l'expérience client.

\textbf{\hfill\break
}

\textbf{Chapitre 5 : Sprint 3 -- Intégration de l'IA pour la
reconnaissance faciale}

\begin{enumerate}
\def\labelenumi{\arabic{enumi}.}
\item
  \textbf{Introduction}
\end{enumerate}

Dans le cadre du projet, ce sprint avait pour objectif d'intégrer un
module d'intelligence artificielle (IA) basé sur la reconnaissance
faciale pour évaluer l'émotion et la satisfaction des utilisateurs.\\
L'approche choisie repose sur un réseau de neurones convolutif (CNN)
entraîné à partir du dataset FER2013, un jeu de données standard
d'images faciales labellisées en 7 classes d'émotions.\\
Ces émotions ont ensuite été regroupées en 3 catégories de satisfaction
:

\begin{itemize}
\item
  \textbf{Satisfait} : Happy, Surprise
\item
  \textbf{Neutre} : Neutral
\item
  \textbf{Insatisfait} : Sad, Angry, Disgust, Fear

  \begin{enumerate}
  \def\labelenumi{\arabic{enumi}.}
  \item
    \textbf{Schéma global du flux}
  \end{enumerate}
\end{itemize}

Le schéma ci-dessous illustre le flux d'intégration de l'IA dans le
système :

\includegraphics[width=1.96181in,height=5.95878in]{media/image25.png}

\begin{enumerate}
\def\labelenumi{\arabic{enumi}.}
\setcounter{enumi}{1}
\item
  \textbf{Technologies et Données}
\end{enumerate}

\begin{enumerate}
\def\labelenumi{\arabic{enumi}.}
\item
  \textbf{Technologies utilisées}
\end{enumerate}

Le projet a mobilisé un ensemble de technologies et de bibliothèques
modernes, adaptées aux besoins du deep learning et du traitement
d'images :

\begin{itemize}
\item
  \textbf{Google Colaboratory (Colab)} : Un environnement basé sur le
  cloud fournissant un accès gratuit à des GPU et TPU. Colab a permis
  d'exécuter le code Python dans un notebook Jupyter, facilitant
  l'exploration interactive des données et l'entraînement accéléré du
  modèle CNN.
\item
  \textbf{Python} : Le langage principal utilisé, reconnu pour sa
  richesse en bibliothèques et sa facilité d'écriture, notamment dans
  les domaines de la data science et du machine learning.
\item
  \textbf{TensorFlow et Keras} : TensorFlow est le framework de deep
  learning sous-jacent, et Keras son API de haut niveau. Ils ont permis
  de construire, entraîner et évaluer le modèle CNN avec une grande
  flexibilité. Keras a facilité la définition de l'architecture du
  modèle et la gestion de l'entraînement.
\item
  \textbf{NumPy} : Utilisé pour les opérations numériques, notamment la
  manipulation de tableaux et de matrices d'images. NumPy a été
  essentiel pour préparer les données avant l'entrée dans le modèle.
\item
  \textbf{Pandas} : Employé pour charger, explorer et manipuler le
  fichier CSV contenant le dataset FER2013. Pandas a simplifié la
  transformation des séquences de pixels en matrices d'images et leur
  étiquetage.
\item
  \textbf{Matplotlib et Seaborn} : Ces bibliothèques ont été utilisées
  pour visualiser les données et les résultats, notamment les
  distributions des classes d'émotions et de satisfaction, les courbes
  d'évolution de la précision et de la perte, et les matrices de
  confusion. Elles ont permis de produire des graphiques clairs et
  explicites.
\item
  \textbf{OpenCV (cv2)} : Utilisé pour le traitement d'images, notamment
  le redimensionnement des images et, dans la partie démonstration, la
  capture et la détection faciale en temps réel via la webcam. Cette
  bibliothèque est un standard dans le domaine de la vision par
  ordinateur.
\item
  \textbf{Scikit-learn} : Employé pour l'évaluation du modèle via des
  métriques telles que la matrice de confusion et le rapport de
  classification. La fonction train\_test\_split de Scikit-learn a
  également été utilisée pour diviser les données entre entraînement et
  validation.
\item
  \textbf{Google Drive} : Intégré à Colab pour le stockage et le
  chargement du dataset. Cela a permis de conserver le fichier CSV du
  dataset FER2013 sur le cloud, assurant un accès rapide et sécurisé.

  \begin{enumerate}
  \def\labelenumi{\arabic{enumi}.}
  \item
    \textbf{Données utilisées : le dataset FER2013}
  \end{enumerate}
\end{itemize}

Le projet s'est appuyé sur le dataset \textbf{FER2013 (Facial Expression
Recognition 2013)}, un standard dans le domaine de la reconnaissance des
émotions faciales.

\begin{enumerate}
\def\labelenumi{\arabic{enumi}.}
\item
  \textbf{Origine}
\end{enumerate}

Le dataset FER2013 est issu de la compétition ICML 2013. Il est
disponible publiquement sur la plateforme Kaggle et contient des images
faciales étiquetées selon sept émotions : \emph{Angry, Disgust, Fear,
Happy, Sad, Surprise, Neutral}.

\begin{enumerate}
\def\labelenumi{\arabic{enumi}.}
\setcounter{enumi}{1}
\item
  \textbf{Contenu}
\end{enumerate}

\begin{itemize}
\item
  Format : Fichier CSV, chaque ligne contient une séquence de pixels
  d'une image de 48x48 (grayscale) et un label associé.
\item
  Nombre total d'images : Environ 35 887 exemples répartis en 7 classes.
\item
  Répartition : Le dataset est divisé en ensembles d'entraînement, de
  validation et de test.

  \begin{enumerate}
  \def\labelenumi{\arabic{enumi}.}
  \item
    \textbf{Prétraitement des données}
  \end{enumerate}

  \begin{enumerate}
  \def\labelenumi{\arabic{enumi}.}
  \item
    \textbf{Chargement et nettoyage}
  \end{enumerate}
\end{itemize}

Les données sont extraites d'un fichier CSV stocké sur Google Drive,
contenant des séquences de pixels correspondant à des images 48x48.
Chaque ligne est transformée en matrice d'image (grayscale) et
normalisée dans l'intervalle {[}0,1{]}.

\includegraphics[width=4.66667in,height=3.80486in]{media/image27.png}

\includegraphics[width=6.3in,height=3.78958in]{media/image29.png}

\includegraphics[width=5.21806in,height=3.54722in]{media/image31.png}

\begin{enumerate}
\def\labelenumi{\arabic{enumi}.}
\setcounter{enumi}{1}
\item
  \textbf{Mapping des labels}
\end{enumerate}

\includegraphics[width=5.83681in,height=4.10833in]{media/image33.png}Les
7 émotions initiales sont regroupées en 3 classes de satisfaction pour
simplifier l'interprétation et aligner le modèle avec les besoins
métier.

\begin{enumerate}
\def\labelenumi{\arabic{enumi}.}
\setcounter{enumi}{2}
\item
  \textbf{Visualisation des distributions}
\end{enumerate}

\includegraphics[width=5.70486in,height=3.41875in]{media/image35.png}Des
histogrammes et des échantillons d'images ont été générés pour
comprendre la répartition des classes et la qualité des données.

\begin{enumerate}
\def\labelenumi{\arabic{enumi}.}
\item
  \textbf{Conception du modèle CNN}
\end{enumerate}

\begin{enumerate}
\def\labelenumi{\arabic{enumi}.}
\item
  \textbf{Architecture}
\end{enumerate}

Le modèle CNN a été conçu pour classifier les expressions faciales en 3
classes de satisfaction. Il s'appuie sur une architecture typique de
\textbf{réseaux de neurones convolutifs}, avec plusieurs blocs pour
extraire les caractéristiques visuelles, suivis de couches denses pour
la classification.

\textbf{Composition générale :}

\begin{itemize}
\item
  \textbf{Entrée :} image 48x48 en niveaux de gris (1 canal).
\item
  \textbf{Blocs convolutifs :} 3 blocs composés chacun :

  \begin{itemize}
  \item
    Convolution (filtrage) avec un nombre croissant de filtres (par ex.
    32, 64, 128), tailles de noyaux (3x3), et activation ReLU.
  \item
    Normalisation des lots (Batch Normalization) pour stabiliser et
    accélérer l'apprentissage.
  \item
    Pooling (par ex. MaxPooling 2x2) pour réduire la taille spatiale.
  \item
    Dropout (par ex. 25\% à 50\%) pour limiter le surapprentissage.
  \end{itemize}
\end{itemize}

\begin{itemize}
\item
  \textbf{Flatten :} transformation des cartes de caractéristiques en un
  vecteur plat.
\end{itemize}

\begin{itemize}
\item
  \textbf{Couches denses :}

  \begin{itemize}
  \item
    Une couche dense intermédiaire (par ex. 128 neurones, ReLU, avec
    dropout).
  \item
    Une couche de sortie (Softmax) avec 3 neurones correspondant aux 3
    classes (faible satisfaction, moyenne satisfaction, haute
    satisfaction).
  \end{itemize}
\end{itemize}

\includegraphics[width=4.8125in,height=1.25208in]{media/image37.jpg}\includegraphics[width=5.65625in,height=1.25208in]{media/image39.jpg}

\includegraphics[width=6.30189in,height=0.88302in]{media/image41.jpg}

\begin{enumerate}
\def\labelenumi{\arabic{enumi}.}
\item
  \textbf{Résumé du modèle}
\end{enumerate}

Le résumé du modèle présente les dimensions de chaque couche, le nombre
de paramètres entraînables et non-entraînables, ainsi que la nature des
activations.

\includegraphics[width=3.85556in,height=4.88056in]{media/image43.png}

\includegraphics[width=3.90764in,height=2.88264in]{media/image45.png}

\begin{enumerate}
\def\labelenumi{\arabic{enumi}.}
\item
  \textbf{Entraînement et Validation}
\end{enumerate}

\begin{enumerate}
\def\labelenumi{\arabic{enumi}.}
\item
  \textbf{Paramètres d'entraînement}
\end{enumerate}

L'entraînement du modèle CNN a été configuré avec les paramètres
suivants :

\begin{itemize}
\item
  \textbf{Optimiseur :} Adam, un algorithme d'optimisation adapté aux
  grands ensembles de données et bien connu pour sa rapidité et sa
  stabilité.
\item
  \textbf{Taux d'apprentissage :} 0.001, ajusté dynamiquement à la
  baisse en fonction des performances sur l'ensemble de validation à
  l'aide du callback ReduceLROnPlateau.
\item
  \textbf{Fonction de perte :} Categorical Crossentropy, adaptée à la
  classification multiclasse avec des probabilités.
\item
  \textbf{Nombre d'époques :} 50, permettant au modèle de suffisamment
  apprendre les représentations sans risquer le surapprentissage.
\item
  \textbf{Batch size :} 64, choisi pour optimiser le compromis entre
  stabilité de l'optimisation et rapidité d'exécution.
\item
  \textbf{Callbacks :}

  \begin{itemize}
  \item
    \emph{EarlyStopping :} arrête l'entraînement si la performance sur
    l'ensemble de validation n'augmente plus (surveillance de la
    validation loss).
  \item
    \emph{ModelCheckpoint :} sauvegarde le modèle ayant les meilleures
    performances sur l'ensemble de validation.
  \item
    \emph{ReduceLROnPlateau :} réduit dynamiquement le taux
    d'apprentissage lorsque la validation loss stagne.
  \end{itemize}

  \begin{enumerate}
  \def\labelenumi{\arabic{enumi}.}
  \item
    \textbf{Résultats entraînement}
  \end{enumerate}
\end{itemize}

L'évolution des performances du modèle sur les ensembles d'entraînement
et de validation a été suivie en fonction du nombre d'époques.\\
La figure suivante montre :

\begin{itemize}
\item
  L'évolution de la précision (accuracy) au fil des époques.
\item
  L'évolution de la perte (loss) pour détecter d'éventuels
  surapprentissages ou problèmes d'ajustement.
\end{itemize}

\includegraphics[width=5.57014in,height=3.95625in]{media/image47.png}\includegraphics[width=5.57222in,height=3.98681in]{media/image49.png}Les
courbes montrent généralement :

\begin{itemize}
\item
  Une précision croissante et une perte décroissante sur l'ensemble
  d'entraînement.
\item
  Une stabilisation des performances sur l'ensemble de validation, qui
  est le signe d'un bon ajustement du modèle.
\item
  Un éventuel écart entre les performances entraînement/validation
  (gap), indiquant un possible surapprentissage à surveiller.

  \begin{enumerate}
  \def\labelenumi{\arabic{enumi}.}
  \item
    \textbf{Évaluation et Résultats}
  \end{enumerate}

  \begin{enumerate}
  \def\labelenumi{\arabic{enumi}.}
  \item
    \textbf{Performance globale sur les données test}
  \end{enumerate}
\end{itemize}

Après l'entraînement du modèle CNN, l'évaluation est réalisée sur
l'ensemble de test (non utilisé durant l'entraînement). Les métriques
principales obtenues sont :

\begin{itemize}
\item
  \textbf{Accuracy (Précision globale) : 78.56\%}
\end{itemize}

Cette mesure indique la proportion d'expressions correctement classées
parmi toutes celles du jeu de test. Une précision élevée reflète la
bonne capacité générale du modèle à distinguer les classes.

\begin{itemize}
\item
  \textbf{Perte (Loss)~: 0.5147}
\end{itemize}

La perte est calculée à l'aide de l'entropie croisée catégorique
(Categorical Crossentropy) et indique l'erreur moyenne commise par le
modèle.

\begin{enumerate}
\def\labelenumi{\arabic{enumi}.}
\item
  \textbf{Matrice de confusion}
\end{enumerate}

La matrice de confusion permet de visualiser comment le modèle se trompe
entre les classes. Chaque case indique le nombre de prédictions pour
chaque combinaison (classe réelle vs classe prédite).

Cet outil est essentiel pour identifier les confusions fréquentes, par
exemple entre des niveaux de satisfaction proches.

\includegraphics[width=4.96944in,height=4.39583in]{media/image51.png}

\begin{enumerate}
\def\labelenumi{\arabic{enumi}.}
\setcounter{enumi}{1}
\item
  \textbf{Rapport de classification}
\end{enumerate}

Le rapport de classification fournit des métriques détaillées pour
chaque classe :

\begin{itemize}
\item
  \textbf{Précision} (Precision) : proportion de vraies prédictions
  positives parmi toutes les prédictions positives.
\item
  \textbf{Rappel} (Recall) : proportion de vraies prédictions positives
  parmi tous les cas positifs réels.
\item
  \textbf{F1-score} : moyenne harmonique de la précision et du rappel.
\end{itemize}

Ce rapport donne un aperçu complet des performances du modèle, y compris
pour des classes minoritaires ou déséquilibrées.

\includegraphics[width=5in,height=2.47917in]{media/image53.png}

\begin{enumerate}
\def\labelenumi{\arabic{enumi}.}
\item
  \textbf{Résumé global}
\end{enumerate}

Le modèle atteint une \textbf{précision globale de 78,56 \%} avec une
\textbf{perte finale de 0,5147}, indiquant une bonne capacité de
généralisation malgré la complexité de la tâche.

La \textbf{matrice de confusion} montre que les classes
\textbf{Satisfied} et \textbf{Unsatisfied} sont bien reconnues, mais le
modèle confond fréquemment la classe \textbf{Neutral}, notamment :

\begin{itemize}
\item
  435 exemples "Neutral" prédits comme "Unsatisfied"
\item
  282 exemples "Satisfied" prédits comme "Unsatisfied"
\end{itemize}

Le \textbf{rapport de classification} révèle :

\begin{itemize}
\item
  De bonnes performances pour \textbf{Satisfied} (F1 = 0.86) et
  \textbf{Unsatisfied} (F1 = 0.80)
\item
  Une performance plus faible pour \textbf{Neutral} (F1 = 0.58)
\item
  Un F1-score moyen macro de 0.75 et pondéré de 0.78

  \begin{enumerate}
  \def\labelenumi{\arabic{enumi}.}
  \item
    \textbf{Résultat sur des images réelles}
  \end{enumerate}
\end{itemize}

Nous avons testé notre modèle sur nos propres images prises dans des
conditions réelles, telles qu'en intérieur et avec différentes
expressions faciales. Le modèle a pu détecter les visages et déterminer
le niveau de satisfaction des individus en se basant sur leurs
expressions. De plus, dans un environnement comme un magasin, nous avons
utilisé notre système pour identifier les zones les plus fréquentées, ce
qui pourrait aider à l'analyse du comportement des clients. Ces
résultats confirment l'efficacité de notre approche sur des images
réelles, à la fois pour la reconnaissance des émotions et pour l'analyse
des zones de fréquentation.

\includegraphics[width=5.21181in,height=3.275in]{media/image55.png}

\includegraphics[width=2.98472in,height=3.63819in]{media/image57.png}

\includegraphics[width=2.97708in,height=3.94931in]{media/image59.png}

\includegraphics[width=3.70556in,height=3.80486in]{media/image61.png}

\begin{enumerate}
\def\labelenumi{\arabic{enumi}.}
\item
  \textbf{Conclusion}
\end{enumerate}

Ce sprint a permis de développer et d'intégrer un modèle d'intelligence
artificielle capable de reconnaître les émotions faciales et d'évaluer
la satisfaction des utilisateurs. En utilisant le dataset FER2013 et des
outils comme TensorFlow et Keras, le modèle a été entraîné avec de bons
résultats, atteignant une précision de près de 79 \%.

Cependant, le modèle a parfois du mal à différencier la classe « Neutre
» des autres catégories, ce qui est un point à améliorer. Malgré cela,
cette étape est importante car elle permet d'analyser automatiquement
les émotions des utilisateurs, ce qui pourra aider à mieux comprendre
leur satisfaction.

Cette réussite ouvre la voie à des améliorations futures pour rendre le
modèle encore plus précis et utile dans le projet.

\textbf{Chapitre 6 : Sprint 4 -- Tests, déploiement et finalisation}

\begin{enumerate}
\def\labelenumi{\arabic{enumi}.}
\item
  \textbf{Introduction}
\end{enumerate}

Ce chapitre décrit le sprint consacré aux tests, au déploiement et à la
finalisation de la plateforme e-commerce pour le showroom de décoration
et de linge de maison. Ce sprint vise à garantir que l'application est
fonctionnelle, stable et prête à répondre aux besoins des utilisateurs
dans un environnement réel. Les tests se concentrent sur la validation
des flux utilisateurs, tandis que le déploiement s'appuie sur des
services cloud modernes pour assurer performance, scalabilité et
sécurité.

\begin{enumerate}
\def\labelenumi{\arabic{enumi}.}
\setcounter{enumi}{1}
\item
  \textbf{Objectifs du Sprint}
\end{enumerate}

L'objectif principal de ce sprint est de finaliser la plateforme pour
une mise en production réussie. Les objectifs spécifiques incluent :

\begin{itemize}
\item
  \textbf{Tests exhaustifs} : Vérifier l'ensemble des fonctionnalités à
  travers des tests automatisés pour valider les parcours utilisateurs.
\item
  \textbf{Déploiement des composants} : Mettre en production le
  frontend, le backend, la base de données et le système
  d'authentification sur des plateformes cloud adaptées.
\item
  \textbf{Correction des anomalies} : Identifier et résoudre les
  problèmes détectés lors des tests pour garantir une expérience fluide.
\item
  \textbf{Finalisation} : S'assurer que la plateforme est prête pour un
  usage réel par les clients et les administrateurs.
\end{itemize}

\begin{enumerate}
\def\labelenumi{\arabic{enumi}.}
\setcounter{enumi}{2}
\item
  \textbf{Tests}

  \begin{enumerate}
  \def\labelenumii{\arabic{enumii}.}
  \item
    \textbf{Tests End-to-End avec Cypress}
  \end{enumerate}
\end{enumerate}

\begin{quote}
Les tests end-to-end ont été effectués à l'aide de Cypress, un outil
permettant de simuler les interactions des utilisateurs avec la
plateforme. Ces tests ont couvert les principaux scénarios d'utilisation
pour les clients et les administrateurs, assurant ainsi une expérience
cohérente et sans erreur.

\textbf{Flux testés pour les clients :}
\end{quote}

\begin{itemize}
\item
  Navigation dans les catégories.
\item
  Interaction avec le chatbot statique.
\item
  Formulaire de contact.
\item
  Inscription à la newsletter.
\item
  Processus de commande.
\end{itemize}

\begin{quote}
\includegraphics[width=6.3in,height=3.23958in]{media/image63.png}
\end{quote}

\begin{enumerate}
\def\labelenumi{\arabic{enumi}.}
\setcounter{enumi}{3}
\item
  \textbf{Déploiement}
\end{enumerate}

Le déploiement de la plateforme a été effectué sur des services cloud
pour garantir scalabilité, disponibilité et performance. Chaque
composant de l'architecture a été déployé sur une plateforme adaptée à
ses besoins.

\begin{enumerate}
\def\labelenumi{\arabic{enumi}.}
\item
  \textbf{Déploiement de l'application Laravel}
\end{enumerate}

Le backend Laravel a été déployé sur Fly.io à l'aide de Docker. Cette
plateforme a été choisie pour sa capacité à gérer des applications
scalables et sa facilité de configuration.

\textbf{Étapes du déploiement :}

\begin{itemize}
\item
  Création d'un conteneur pour encapsuler l'application Laravel et ses
  dépendances.
\item
  Configuration des paramètres de déploiement pour définir les
  ressources nécessaires et les régions géographiques.
\item
  Mise en place d'un processus pour automatiser les mises à jour du
  backend.

  \begin{enumerate}
  \def\labelenumi{\arabic{enumi}.}
  \item
    \textbf{Déploiement de Keycloak}
  \end{enumerate}
\end{itemize}

Keycloak a été déployé sur IBM CodeEngine à l'aide de Docker, avec sa
base de données hébergée sur AWS RDS.

\textbf{Déploiement sur IBM CodeEngine :}

\begin{itemize}
\item
  Un conteneur a été configuré pour exécuter Keycloak, avec le realm
  "jiheneline" et les clients nécessaires (backoffice-client,
  frontoffice-client, api-client).
\item
  Les processus de mise à jour ont été automatisés pour maintenir la
  cohérence.
\end{itemize}

\textbf{Base de données sur AWS RDS :}

\begin{itemize}
\item
  Une instance PostgreSQL a été créée pour stocker les données de
  Keycloak (utilisateurs, rôles, sessions).
\item
  Des mesures de sécurité, telles que le chiffrement des données et des
  sauvegardes régulières, ont été mises en place.

  \begin{enumerate}
  \def\labelenumi{\arabic{enumi}.}
  \item
    \textbf{Déploiement de la Base de Données de l'application}
  \end{enumerate}
\end{itemize}

La base de données de l'application Laravel a été déployée sur
\textbf{NeonDB}, une solution PostgreSQL serverless adaptée aux besoins
de scalabilité.

\textbf{Configuration NeonDB :}

\begin{itemize}
\item
  Création de la base de données avec application des migrations
  Laravel.
\item
  Configuration des connexions sécurisées entre Laravel et NeonDB.
\item
  Activation de fonctionnalités comme les sauvegardes automatiques et la
  gestion des pics de charge.

  \begin{enumerate}
  \def\labelenumi{\arabic{enumi}.}
  \item
    \textbf{Déploiement du Frontoffice et du Backoffice}
  \end{enumerate}
\end{itemize}

Le frontoffice (interface client) et le backoffice (interface
administrateur) ont été déployés sur \textbf{AWS Amplify}, une
plateforme optimisée pour les applications web modernes.

\textbf{Déploiement sur AWS Amplify :}

\begin{itemize}
\item
  Connexion du code source à AWS Amplify pour des déploiements
  automatisés.
\item
  Configuration des domaines pour le frontoffice et le backoffice.
\item
  Optimisation des performances grâce à un réseau de distribution de
  contenu (CDN) et des certificats SSL pour sécuriser les connexions.
\end{itemize}

\begin{enumerate}
\def\labelenumi{\arabic{enumi}.}
\setcounter{enumi}{4}
\item
  \textbf{Conclusion}
\end{enumerate}

Ce sprint a permis de finaliser la plateforme e-commerce en validant ses
fonctionnalités à travers des tests end-to-end avec Cypress et en la
déployant sur une infrastructure cloud robuste. Les déploiements sur
Fly.io, IBM CodeEngine, AWS RDS, NeonDB et AWS Amplify garantissent une
solution scalable et sécurisée, prête à répondre aux besoins des clients
et des administrateurs. Ce sprint marque l'achèvement du développement
initial, posant des bases solides pour une utilisation en conditions
réelles et pour de futures améliorations, telles que l'optimisation de
l'IA ou l'extension à d'autres showrooms.

\end{document}
